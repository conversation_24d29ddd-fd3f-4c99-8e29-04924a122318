// src/app/test-gradient-button/page.tsx
"use client";

import React from "react";
import { GradientButton } from "@/components/ui/gradient-button";
import { Download, Send, Heart, Home, Settings, User, ArrowRight } from "lucide-react";

export default function TestGradientButtonPage() {
    return (
        <div className="container mx-auto p-8 space-y-12">
            <div className="text-center space-y-4">
                <h1 className="text-4xl font-bold">GradientButton Icon Positioning Test</h1>
                <p className="text-muted-foreground">
                    Testing the fixed icon positioning functionality
                </p>
            </div>

            {/* Icon Position Tests */}
            <section className="space-y-6">
                <h2 className="text-2xl font-semibold">Icon Position Tests</h2>
                
                <div className="space-y-4">
                    <h3 className="text-lg font-medium">Left Icon Position</h3>
                    <div className="flex flex-wrap gap-4">
                        <GradientButton icon={<Download />} iconPosition="left" size="sm">
                            Download (SM)
                        </GradientButton>
                        <GradientButton icon={<Download />} iconPosition="left" size="default">
                            Download (Default)
                        </GradientButton>
                        <GradientButton icon={<Download />} iconPosition="left" size="lg">
                            Download (LG)
                        </GradientButton>
                        <GradientButton icon={<Download />} iconPosition="left" size="xl">
                            Download (XL)
                        </GradientButton>
                    </div>
                </div>

                <div className="space-y-4">
                    <h3 className="text-lg font-medium">Right Icon Position</h3>
                    <div className="flex flex-wrap gap-4">
                        <GradientButton icon={<Send />} iconPosition="right" size="sm">
                            Send (SM)
                        </GradientButton>
                        <GradientButton icon={<Send />} iconPosition="right" size="default">
                            Send (Default)
                        </GradientButton>
                        <GradientButton icon={<Send />} iconPosition="right" size="lg">
                            Send (LG)
                        </GradientButton>
                        <GradientButton icon={<Send />} iconPosition="right" size="xl">
                            Send (XL)
                        </GradientButton>
                    </div>
                </div>

                <div className="space-y-4">
                    <h3 className="text-lg font-medium">No Icon Position (Default)</h3>
                    <div className="flex flex-wrap gap-4">
                        <GradientButton icon={<Heart />} iconPosition="none" size="sm">
                            Like (SM)
                        </GradientButton>
                        <GradientButton icon={<Heart />} iconPosition="none" size="default">
                            Like (Default)
                        </GradientButton>
                        <GradientButton icon={<Heart />} iconPosition="none" size="lg">
                            Like (LG)
                        </GradientButton>
                        <GradientButton icon={<Heart />} iconPosition="none" size="xl">
                            Like (XL)
                        </GradientButton>
                    </div>
                </div>
            </section>

            {/* Variant Tests */}
            <section className="space-y-6">
                <h2 className="text-2xl font-semibold">Variant Tests with Icons</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="space-y-4">
                        <h3 className="text-lg font-medium">Primary Variant</h3>
                        <div className="space-y-2">
                            <GradientButton variant="primary" icon={<Home />} iconPosition="left">
                                Home Left
                            </GradientButton>
                            <GradientButton variant="primary" icon={<ArrowRight />} iconPosition="right">
                                Home Right
                            </GradientButton>
                        </div>
                    </div>
                    
                    <div className="space-y-4">
                        <h3 className="text-lg font-medium">Secondary Variant</h3>
                        <div className="space-y-2">
                            <GradientButton variant="secondary" icon={<Settings />} iconPosition="left">
                                Settings Left
                            </GradientButton>
                            <GradientButton variant="secondary" icon={<ArrowRight />} iconPosition="right">
                                Settings Right
                            </GradientButton>
                        </div>
                    </div>
                    
                    <div className="space-y-4">
                        <h3 className="text-lg font-medium">Outline Variant</h3>
                        <div className="space-y-2">
                            <GradientButton variant="outline" icon={<User />} iconPosition="left">
                                Profile Left
                            </GradientButton>
                            <GradientButton variant="outline" icon={<ArrowRight />} iconPosition="right">
                                Profile Right
                            </GradientButton>
                        </div>
                    </div>
                </div>
            </section>

            {/* Full Width Tests */}
            <section className="space-y-6">
                <h2 className="text-2xl font-semibold">Full Width Tests</h2>
                <div className="space-y-4 max-w-md">
                    <GradientButton fullWidth icon={<Download />} iconPosition="left">
                        Full Width Left Icon
                    </GradientButton>
                    <GradientButton fullWidth icon={<Send />} iconPosition="right">
                        Full Width Right Icon
                    </GradientButton>
                    <GradientButton fullWidth icon={<Heart />} iconPosition="none">
                        Full Width No Position
                    </GradientButton>
                </div>
            </section>

            {/* Loading State Tests */}
            <section className="space-y-6">
                <h2 className="text-2xl font-semibold">Loading State Tests</h2>
                <div className="flex flex-wrap gap-4">
                    <GradientButton loading icon={<Download />} iconPosition="left">
                        Loading Left
                    </GradientButton>
                    <GradientButton loading icon={<Send />} iconPosition="right">
                        Loading Right
                    </GradientButton>
                    <GradientButton loading loadingText="Processing..." icon={<Heart />} iconPosition="left">
                        Custom Loading Text
                    </GradientButton>
                </div>
            </section>

            {/* Comparison with Manual CSS */}
            <section className="space-y-6">
                <h2 className="text-2xl font-semibold">Comparison: Fixed vs Manual CSS</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                        <h3 className="text-lg font-medium text-green-600">✅ Fixed Component (Should work)</h3>
                        <div className="space-y-2">
                            <GradientButton icon={<ArrowRight />} iconPosition="right">
                                Fixed Right Icon
                            </GradientButton>
                            <GradientButton icon={<Download />} iconPosition="left">
                                Fixed Left Icon
                            </GradientButton>
                        </div>
                    </div>
                    
                    <div className="space-y-4">
                        <h3 className="text-lg font-medium text-blue-600">🔧 With Manual CSS (Previous workaround)</h3>
                        <div className="space-y-2">
                            <GradientButton 
                                icon={<ArrowRight />} 
                                iconPosition="right"
                                className="flex flex-row items-center justify-center"
                            >
                                Manual CSS Right
                            </GradientButton>
                            <GradientButton 
                                icon={<Download />} 
                                iconPosition="left"
                                className="flex flex-row items-center justify-center"
                            >
                                Manual CSS Left
                            </GradientButton>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    );
}
