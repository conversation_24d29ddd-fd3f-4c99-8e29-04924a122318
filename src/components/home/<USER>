"use client";

import * as React from "react";
import Link from "next/link";
import { ArrowRight } from "lucide-react";
import { AnimatedButton } from "@/components/ui/animated-button";
import { cn } from "@/lib/utils";
import { GradientButton } from "@/components/ui/gradient-button";

export default function CarCharterSection(): React.JSX.Element {
    return (
        <section
            id="car-charter"
            className="relative w-full py-8 text-special-card-fg my-8 md:my-16"
        >
            {/* Left gradient */}
            <div
                className={cn(
                    "pointer-events-none absolute size-full inset-0 max-w-sm md:max-w-screen-md mx-auto shadow-xl shadow-black/50 rounded-[20px] border-y border-[#2C5263]",
                    "bg-gradient-to-l from-background via-[#1D3844] to-background ",
                    // "bg-[linear-gradient(to_right,transparent_0%,#1D3844_25%,#1D3844_50%,#1D3844_75%,transparent_100%)]",
                )}
                aria-hidden="true"
            />

            <div className="relative flex flex-col items-center h-full text-center px-8 md:px-12 max-w-screen-md mx-auto z-10 space-y-6">
                <div className="inline-block bg-primary text-primary-foreground px-3 py-1 rounded-full text-sm font-semibold shadow-lg shadow-black/50">
                    Our Premier Service
                </div>
                <h2 className="text-3xl font-bold tracking-light sm:text-4xl md:text-5xl font-headline">
                    Explore Bali Your Way
                </h2>
                <p className="mt-4 text-lg md:text-xl text-special-card-fg/80">
                    Discover the ultimate freedom with your own private car and
                    English-speaking driver. Create your custom itinerary and
                    explore Bali&#39;s hidden gems, famous landmarks, and
                    everything in between at your own pace.
                </p>
                <div className="mt-6">
                    <AnimatedButton
                        className={cn(
                            "text-[#ffffff] px-1 py-5 shadow-lg shadow-black/50 border-none",
                        )}
                        variant="outline"
                        size="default"
                        glow={false}
                        textEffect="normal"
                        uppercase={true}
                        rounded="custom"
                        asChild={false}
                        hideAnimations={false}
                        shimmerColor="#278ba2"
                        shimmerSize="0.1em"
                        shimmerDuration="3s"
                        borderRadius="12px"
                        background=""
                    >
                        <GradientButton
                            variant={"primary"}
                            size="sm"
                            icon={<ArrowRight className="h-4 w-4" />}
                            iconPosition="right"
                            fullWidth={true}
                            hapticFeedback={true}
                            className={cn(
                                "shadow-lg shadow-black/50 flex flex-row items-center justify-center hover:shadow-black/50 hover:scale-[1.04] scale-[1.01]",
                            )}
                            aria-label="View charter options"
                            aria-describedby="charter-options"
                            aria-expanded={false}
                            aria-pressed={true}
                            textShadow="medium"
                        >
                            <Link href="/private-car-charter">
                                View Charter Options
                                {/* <ArrowRight className="h-4 w-4 " /> */}
                            </Link>
                        </GradientButton>
                    </AnimatedButton>
                </div>
            </div>
        </section>
    );
}
